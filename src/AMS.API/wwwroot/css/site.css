/* Custom styles for AMS application */

html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  padding-top: 76px; /* Account for sticky navbar height */
  margin-bottom: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Sticky navbar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1030;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Main content area */
.main-content {
  flex: 1;
  padding-bottom: 2rem;
}

/* Full width container */
.container-full {
  width: 100%;
  max-width: none;
  padding-left: 0;
  padding-right: 0;
}

/* Dashboard layout with sidebar */
.dashboard-layout {
  display: flex;
  min-height: calc(100vh - 76px); /* Account for navbar */
}

.dashboard-sidebar {
  width: 250px;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  padding: 1rem 0 120px 0; /* Bottom padding for user info */
  position: fixed;
  top: 76px; /* Below navbar */
  left: 0;
  height: calc(100vh - 76px);
  overflow-y: auto;
  z-index: 1020;
}

.dashboard-content {
  flex: 1;
  margin-left: 250px; /* Account for sidebar width */
  padding: 1.5rem;
  min-height: calc(100vh - 76px);
}

/* Sidebar navigation */
.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav .nav-item {
  margin-bottom: 0.25rem;
}

.sidebar-nav .nav-link {
  display: block;
  padding: 0.75rem 1.5rem;
  color: #495057;
  text-decoration: none;
  border-radius: 0;
  transition: all 0.2s ease-in-out;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
  background-color: #e9ecef;
  color: #0d6efd;
}

.sidebar-nav .nav-link i {
  width: 20px;
  margin-right: 0.5rem;
}

/* Sidebar user info at bottom */
.sidebar-user-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.sidebar-user-info .user-dropdown-toggle {
  text-decoration: none;
  color: #6c757d;
  border: none;
  background: none;
}

.sidebar-user-info .user-dropdown-toggle:hover {
  color: #495057;
  background-color: #e9ecef;
  border-radius: 0.375rem;
}

.sidebar-user-info .user-dropdown-toggle:focus {
  box-shadow: none;
}

.sidebar-user-info .dropdown-menu {
  margin-bottom: 0.5rem;
}

/* Footer improvements */
.footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 1.5rem 0;
  margin-top: auto;
}

/* Footer adjustments for dashboard layout with sidebar */
.dashboard-layout + .footer {
  margin-left: 250px; /* Account for sidebar width */
  transition: margin-left 0.3s ease-in-out;
}

/* Footer mobile adjustments */
@media (max-width: 768px) {
  .dashboard-layout + .footer {
    margin-left: 0; /* No sidebar offset on mobile */
  }

  .footer .row {
    text-align: center;
  }

  .footer .col-md-6:last-child {
    text-align: center !important;
    margin-top: 0.5rem;
  }

  /* Ensure footer content is readable on small screens */
  .footer .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .footer a {
    display: inline-block;
    margin: 0.25rem 0;
  }
}

/* Ensure footer stays at bottom for dashboard pages */
.dashboard-layout {
  min-height: calc(100vh - 76px - 120px); /* Account for navbar and footer */
}

/* Additional footer spacing improvements */
.footer .py-3 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

/* Custom card styles */
.card-stats {
  border-left: 4px solid #007bff;
}

.card-stats.success {
  border-left-color: #28a745;
}

.card-stats.warning {
  border-left-color: #ffc107;
}

.card-stats.danger {
  border-left-color: #dc3545;
}

.card-stats.info {
  border-left-color: #17a2b8;
}

/* Dashboard styles */
.dashboard-card {
  transition: transform 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

/* Form styles */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: .65;
  transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Table styles */
.table-hover tbody tr:hover {
  background-color: rgba(0,0,0,.075);
}

/* Status badges */
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.status-pending {
  background-color: #ffc107;
  color: #000;
}

.status-inprogress {
  background-color: #17a2b8;
  color: #fff;
}

.status-resolved {
  background-color: #28a745;
  color: #fff;
}

.status-closed {
  background-color: #6c757d;
  color: #fff;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom button styles */
.btn-outline-primary:hover {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  .navbar-nav .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }

  /* Mobile sidebar adjustments */
  .dashboard-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    z-index: 1040;
    top: 0; /* Full height on mobile */
    height: 100vh; /* Full viewport height */
    width: 280px; /* Slightly wider on mobile */
  }

  .dashboard-sidebar.show {
    transform: translateX(0);
  }

  .dashboard-content {
    margin-left: 0;
    padding: 1rem;
  }

  /* Mobile sidebar toggle in navbar */
  .sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.15s ease-in-out;
  }

  .sidebar-toggle-btn:hover,
  .sidebar-toggle-btn:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .sidebar-toggle-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
  }

  /* Ensure proper spacing in navbar with sidebar toggle */
  .navbar-brand {
    margin-left: 0;
  }

  /* Mobile menu for homepage */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1030;
    display: none;
  }

  .mobile-menu-overlay.show {
    display: block;
  }

  .mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 280px;
    height: 100vh;
    background-color: white;
    z-index: 1040;
    transition: right 0.3s ease-in-out;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    padding-top: 76px; /* Account for navbar height */
  }

  .mobile-menu.show {
    right: 0;
  }

  .mobile-menu-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    font-weight: 500;
  }

  .mobile-menu-items {
    padding: 1rem 0;
  }

  .mobile-menu-item {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #495057;
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    transition: background-color 0.2s ease-in-out;
  }

  .mobile-menu-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
  }

  .mobile-menu-item.logout-btn {
    color: #dc3545;
  }

  .mobile-menu-item.logout-btn:hover {
    background-color: #f8d7da;
    color: #721c24;
  }

  .mobile-menu-divider {
    height: 1px;
    background-color: #dee2e6;
    margin: 0.5rem 1.5rem;
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1030;
    display: none;
  }

  .sidebar-overlay.show {
    display: block;
  }
}



/* Alert improvements */
.alert {
  border: none;
  border-radius: 0.5rem;
}

.alert-success {
  background-color: #d1e7dd;
  color: #0a3622;
}

.alert-danger {
  background-color: #f8d7da;
  color: #58151c;
}

.alert-info {
  background-color: #d1ecf1;
  color: #055160;
}

/* Navigation improvements */
.navbar-brand {
  font-weight: 600;
  font-size: 1.5rem;
}

.nav-link {
  font-weight: 500;
}

.dropdown-item {
  font-weight: 400;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

/* Card enhancements */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

/* Form enhancements */
.form-control, .form-select {
  border-radius: 0.375rem;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button enhancements */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.btn-sm {
  font-size: 0.875rem;
}

/* Utility classes */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cursor-pointer {
  cursor: pointer;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Additional utility classes */
.sidebar-section {
  padding: 1rem 1.5rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-divider {
  height: 1px;
  background-color: #dee2e6;
  margin: 0.5rem 1rem;
}

/* Content wrapper for non-dashboard pages */
.content-wrapper {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Full width content for dashboard */
.content-full {
  padding: 0;
  max-width: none;
}

/* Navbar brand improvements */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.025em;
}

/* Card hover effects for dashboard */
.dashboard-card {
  transition: all 0.2s ease-in-out;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Improved spacing */
.section-spacing {
  margin-bottom: 2rem;
}

.content-spacing {
  margin-bottom: 1.5rem;
}
