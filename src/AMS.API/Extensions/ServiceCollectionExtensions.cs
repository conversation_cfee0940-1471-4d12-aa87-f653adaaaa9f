using System.Reflection;
using System.Text;
using System.Text.Json;
using FluentValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.Infrastructure.Data;
using AMS.Infrastructure.Repositories;
using AMS.Infrastructure.Services;
using AMS.API.Filters;
using Microsoft.Extensions.Configuration;

namespace AMS.API.Extensions;

/// <summary>
/// Extension methods for IServiceCollection to configure application services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Configures database services
    /// </summary>
    public static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration configuration)
    {
        // .NET automatically handles environment variable substitution
        // Environment variables override appsettings values using the __ notation
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        if (string.IsNullOrEmpty(connectionString) || connectionString.Contains("PLACEHOLDER"))
        {
            throw new InvalidOperationException(
                "Database connection string is not properly configured. " +
                "Please set the ConnectionStrings__DefaultConnection environment variable " +
                "or configure ConnectionStrings:DefaultConnection in appsettings.json with valid values.");
        }

        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName);
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
            });

            // Enable sensitive data logging in development
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")
                ?? configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT");

            if (environment == "Development")
            {
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            }
        });

        services.AddScoped<DatabaseSeeder>();

        return services;
    }

    /// <summary>
    /// Configures repository services
    /// </summary>
    public static IServiceCollection AddRepositoryServices(this IServiceCollection services)
    {
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<ISupportQuestionRepository, SupportQuestionRepository>();

        return services;
    }

    /// <summary>
    /// Configures application services
    /// </summary>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<IAuthenticationService, AuthenticationService>();
        services.AddScoped<ISupportQuestionService, SupportQuestionService>();

        return services;
    }

    /// <summary>
    /// Configures validation services
    /// </summary>
    public static IServiceCollection AddValidationServices(this IServiceCollection services)
    {
        services.AddValidatorsFromAssemblyContaining<Program>();

        return services;
    }

    /// <summary>
    /// Configures Swagger/OpenAPI documentation
    /// </summary>
    public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
    {
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "AMS API",
                Version = "v1.0.0",
                Description = "A comprehensive AMS WebAPI built with Clean Architecture principles",
                Contact = new OpenApiContact
                {
                    Name = "AMS Development Team",
                    Email = "<EMAIL>",
                    Url = new Uri("https://github.com/your-org/ams")
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                }
            });

            // Add JWT Authentication to Swagger
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Enter 'Bearer' [space] and then your token in the text input below.",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer",
                BearerFormat = "JWT"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // Include XML comments
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }

            // Add operation filters for better documentation
            c.OperationFilter<SwaggerDefaultValues>();
            c.DocumentFilter<SwaggerDocumentFilter>();

            // Configure schema generation
            c.SchemaFilter<SwaggerSchemaFilter>();
        });

        return services;
    }

    /// <summary>
    /// Configures authentication and authorization services
    /// </summary>
    public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)
    {
        // .NET automatically handles environment variable substitution using the __ notation
        var jwtSettings = configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"];
        var issuer = jwtSettings["Issuer"] ?? "AMS";
        var audience = jwtSettings["Audience"] ?? "AMS";
        var expirationMinutes = int.TryParse(jwtSettings["ExpirationMinutes"], out var expiration) ? expiration : 60;

        if (string.IsNullOrEmpty(secretKey) || secretKey.Contains("PLACEHOLDER"))
        {
            throw new InvalidOperationException(
                "JWT SecretKey is not properly configured. " +
                "Please set the JwtSettings__SecretKey environment variable " +
                "or configure JwtSettings:SecretKey in appsettings.json with a valid secret key.");
        }

        services.AddAuthentication(options =>
        {
            // Default to Cookie for web pages
            options.DefaultAuthenticateScheme = "Cookies";
            options.DefaultChallengeScheme = "Cookies";
            options.DefaultSignInScheme = "Cookies";
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
                ValidateIssuer = true,
                ValidIssuer = issuer,
                ValidateAudience = true,
                ValidAudience = audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero,
                RequireExpirationTime = true
            };

            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                    {
                        context.Response.Headers["Token-Expired"] = "true";
                    }
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    context.HandleResponse();
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "application/json";
                    var result = JsonSerializer.Serialize(new
                    {
                        success = false,
                        message = "You are not authorized to access this resource",
                        timestamp = DateTime.UtcNow
                    });
                    return context.Response.WriteAsync(result);
                }
            };
        })
        .AddCookie("Cookies", options =>
        {
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";
            options.AccessDeniedPath = "/Account/AccessDenied";
            options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
            options.SlidingExpiration = true;
            options.Cookie.Name = "AMS.Auth";
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
        });

        services.AddAuthorization(options =>
        {
            // API policies (use JWT)
            options.AddPolicy(ApplicationConstants.Policies.RequireAdministratorRole, policy =>
            {
                policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                policy.RequireRole(ApplicationConstants.Roles.Administrator);
            });

            options.AddPolicy(ApplicationConstants.Policies.RequireManagerRole, policy =>
            {
                policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                policy.RequireRole(ApplicationConstants.Roles.Manager, ApplicationConstants.Roles.Administrator);
            });

            options.AddPolicy(ApplicationConstants.Policies.RequireUserRole, policy =>
            {
                policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                policy.RequireRole(ApplicationConstants.Roles.User, ApplicationConstants.Roles.Manager, ApplicationConstants.Roles.Administrator);
            });

            // Web UI policies (use Cookies)
            options.AddPolicy("WebAdministrator", policy =>
            {
                policy.AuthenticationSchemes.Add("Cookies");
                policy.RequireRole(ApplicationConstants.Roles.Administrator);
            });

            options.AddPolicy("WebManager", policy =>
            {
                policy.AuthenticationSchemes.Add("Cookies");
                policy.RequireRole(ApplicationConstants.Roles.Manager, ApplicationConstants.Roles.Administrator);
            });

            options.AddPolicy("WebUser", policy =>
            {
                policy.AuthenticationSchemes.Add("Cookies");
                policy.RequireRole(ApplicationConstants.Roles.User, ApplicationConstants.Roles.Manager, ApplicationConstants.Roles.Administrator);
            });
        });

        return services;
    }
}
