<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - AMS</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-building"></i>
                    AMS
                </a>

                <!-- Desktop navigation - always visible on desktop -->
                <div class="d-md-flex align-items-center ms-auto">
                    @if (User.Identity?.IsAuthenticated == true && !string.IsNullOrEmpty(User.Identity.Name))
                    {
                        <div class="dropdown">
                            <button class="btn btn-link nav-link dropdown-toggle text-white text-decoration-none border-0 bg-transparent p-2"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-2"></i>
                                <span>@User.Identity.Name</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" asp-controller="Home" asp-action="Dashboard">
                                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                                </a></li>
                                <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                    <i class="bi bi-person me-2"></i>Profile
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    }
                    else
                    {
                        <a class="nav-link text-white" asp-controller="Account" asp-action="Login">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Login
                        </a>
                    }
                </div>
            </div>
        </nav>
    </header>
    
    <div class="main-content">
        <div class="content-wrapper">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle"></i> @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @RenderBody()
        </div>
    </div>

    <footer class="footer text-muted">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    &copy; @DateTime.Now.Year - AMS
                </div>
                <div class="col-md-6 text-end">
                    <a href="/swagger" class="text-muted me-3" target="_blank">
                        <i class="bi bi-code-slash"></i> API Documentation
                    </a>
                    <span class="text-muted">Version 1.0</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        // Mobile menu functionality for homepage
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const overlay = document.querySelector('.mobile-menu-overlay');

            if (mobileMenu && overlay) {
                mobileMenu.classList.toggle('show');
                overlay.classList.toggle('show');
            }
        }

        function closeMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const overlay = document.querySelector('.mobile-menu-overlay');

            if (mobileMenu && overlay) {
                mobileMenu.classList.remove('show');
                overlay.classList.remove('show');
            }
        }
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
