<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - AMS Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-dark bg-primary">
            <div class="container-fluid">
                <!-- Single burger menu for sidebar toggle -->
                <button class="navbar-toggler sidebar-toggle-btn d-md-none me-2" type="button" onclick="AMS.sidebar.toggle()" aria-label="Toggle menu">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-building"></i>
                    AMS
                </a>

                <!-- Desktop account info (hidden on mobile) -->
                @if (User.Identity?.IsAuthenticated == true)
                {
                    <div class="d-none d-md-flex align-items-center text-white">
                        <i class="bi bi-person-circle me-2"></i>
                        <span>@User.Identity.Name</span>
                    </div>
                }
            </div>
        </nav>
    </header>

    <!-- Sidebar overlay for mobile -->
    <div class="sidebar-overlay" onclick="AMS.sidebar.close()"></div>

    <div class="dashboard-layout">
        <!-- Sidebar -->
        <nav class="dashboard-sidebar" id="dashboardSidebar">
            <div class="sidebar-section">Management</div>
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Dashboard" ? "active" : "")"
                    asp-controller="Home" asp-action="Dashboard">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
            @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
            {
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "SupportQuestions" && ViewContext.RouteData.Values["action"]?.ToString() == "Index" ? "active" : "")"
                        asp-controller="SupportQuestions" asp-action="Index">
                        <i class="bi bi-chat-dots"></i>
                        Support Questions
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Users" ? "active" : "")"
                        asp-controller="Users" asp-action="Index">
                        <i class="bi bi-people"></i>
                        User Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/swagger" target="_blank">
                        <i class="bi bi-code-slash"></i>
                        API Documentation
                    </a>
                </li>
            }
            </ul>

             <!-- Account Section -->
            <div class="sidebar-section">Account</div>
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Account" && ViewContext.RouteData.Values["action"]?.ToString() == "Profile" ? "active" : "")"
                       asp-controller="Account" asp-action="Profile">
                        <i class="bi bi-person"></i>
                        Profile
                    </a>
                </li>
            </ul>

            <!-- User Info & Logout (Bottom of Sidebar) -->
            <div class="sidebar-user-info">
                @if (User.Identity?.IsAuthenticated == true)
                {
                    <div class="user-email">
                        <i class="bi bi-person-circle me-2"></i>
                        <small class="text-muted">@User.Identity.Name</small>
                    </div>
                    <div class="logout-section">
                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline w-100">
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </button>
                        </form>
                    </div>
                }
            </div>
        </nav>

        <!-- Main content -->
        <main class="dashboard-content">
            <!-- Alert messages -->
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle"></i> @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            
            @RenderBody()
        </main>
    </div>

    <footer class="footer text-muted">
        <div class="container-fluid">
            <div class="row py-3">
                <div class="col-md-6">
                    &copy; @DateTime.Now.Year - AMS
                </div>
                <div class="col-md-6 text-end">
                    <a href="/swagger" class="text-muted me-3" target="_blank">
                        <i class="bi bi-code-slash"></i> API Documentation
                    </a>
                    <span class="text-muted">Version 1.0</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        // Update overlay click to use AMS.sidebar.close
        document.querySelector('.sidebar-overlay').addEventListener('click', AMS.sidebar.close);
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
